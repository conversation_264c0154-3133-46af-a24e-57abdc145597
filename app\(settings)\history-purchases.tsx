import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity, Modal} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import styles from "@/styles/settings/history-purchases.style";

interface Purchase {
  id: string;
  type: "event" | "subscription" | "product" | "service";
  title: string;
  description: string;
  amount: number;
  date: string;
  status: "completed" | "pending" | "cancelled" | "refunded";
  paymentMethod: string;
  itemType: string;
  orderNumber: string;
}

const HistoryPurchases: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<string>("all");
  const [selectedPurchase, setSelectedPurchase] = useState<Purchase | null>(
    null
  );
  const [purchases] = useState<Purchase[]>([
    {
      id: "1",
      type: "subscription",
      title: "Comemoração de 10 anos da Cafeteira Garibaldi",
      description: "",
      amount: 1500.0,
      date: "2024-05-22",
      status: "pending",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "2",
      type: "event",
      title: "Encontro anual de empreendedores em Balneário Camboriú",
      description: "",
      amount: 1200.0,
      date: "2024-05-21",
      status: "completed",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "3",
      type: "subscription",
      title: "Assinatura mensal de membro Club M",
      description: "",
      amount: 280.0,
      date: "2024-05-20",
      status: "completed",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "4",
      type: "service",
      title: "Mentoria Coletiva sobre Resoluções Jurídicas",
      description: "",
      amount: 1200.0,
      date: "2024-04-15",
      status: "cancelled",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    },
    {
      id: "5",
      type: "subscription",
      title: "Assinatura mensal de membro Club M",
      description: "",
      amount: 280.0,
      date: "2024-04-10",
      status: "completed",
      paymentMethod: "Mastercard Crédito (final 8449)",
      itemType: "Produto Club M",
      orderNumber: "1d9w-m3Jw-vdRp-r2H"
    }
  ]);

  const allTabs = [
    {id: "all", name: "Todas as compras"},
    {id: "completed", name: "Compras efetuadas"},
    {id: "pending", name: "Compras pendentes"}
  ];

  // Reorder tabs to put the selected tab first
  const tabs = [
    allTabs.find((tab) => tab.id === selectedTab)!,
    ...allTabs.filter((tab) => tab.id !== selectedTab)
  ];

  const filteredPurchases =
    selectedTab === "all"
      ? purchases
      : purchases.filter((purchase) => purchase.status === selectedTab);

  // Group purchases by month
  const groupedPurchases = filteredPurchases.reduce((groups, purchase) => {
    const date = new Date(purchase.date);
    const currentDate = new Date();

    let monthKey;
    if (
      date.getMonth() === currentDate.getMonth() &&
      date.getFullYear() === currentDate.getFullYear()
    ) {
      monthKey = "Mês atual";
    } else {
      monthKey = date.toLocaleDateString("pt-BR", {
        month: "long",
        year: "numeric"
      });
      monthKey = monthKey.charAt(0).toUpperCase() + monthKey.slice(1);
    }

    if (!groups[monthKey]) {
      groups[monthKey] = [];
    }
    groups[monthKey].push(purchase);
    return groups;
  }, {} as Record<string, Purchase[]>);

  const getStatusStyles = (status: string) => {
    switch (status) {
      case "completed":
        return {
          container: styles.completedStatus,
          text: styles.completedText
        };
      case "cancelled":
        return {
          container: styles.cancelledStatus,
          text: styles.cancelledText
        };
      case "pending":
      default:
        return {
          container: styles.pendingStatus,
          text: styles.pendingText
        };
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Efetuada";
      case "pending":
        return "Pendente";
      case "cancelled":
        return "Cancelada";
      case "refunded":
        return "Reembolsado";
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit"
    });
  };

  return (
    <ScreenWithHeader screenTitle="Histórico de compras" backButton>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[styles.tab, selectedTab === tab.id && styles.activeTab]}
                onPress={() => setSelectedTab(tab.id)}
              >
                <Text
                  style={[
                    styles.tabText,
                    selectedTab === tab.id && styles.activeTabText
                  ]}
                >
                  {tab.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Purchase List */}
          <ScrollView style={{flex: 1}}>
            {Object.entries(groupedPurchases).map(
              ([monthKey, monthPurchases], index) => (
                <View key={monthKey}>
                  {/* Month Label */}
                  <Text style={styles.monthLabel}>{monthKey}</Text>

                  {monthPurchases.map((purchase) => (
                    <TouchableOpacity
                      key={purchase.id}
                      style={styles.purchaseItem}
                      onPress={() => setSelectedPurchase(purchase)}
                    >
                      <View style={styles.purchaseIcon}>
                        <Text style={{fontSize: 16, color: "#FFFFFF"}}>📄</Text>
                      </View>

                      <View style={styles.purchaseContent}>
                        <Text
                          style={styles.purchaseTitle}
                          numberOfLines={2}
                          ellipsizeMode="tail"
                        >
                          {purchase.title}
                        </Text>
                      </View>

                      <View style={styles.purchaseRight}>
                        <Text
                          style={[
                            styles.statusText,
                            getStatusStyles(purchase.status).text
                          ]}
                        >
                          {getStatusText(purchase.status)}
                        </Text>
                        <Text
                          style={[
                            styles.purchasePrice,
                            purchase.status === "cancelled" &&
                              styles.cancelledPrice
                          ]}
                        >
                          R$ {purchase.amount.toFixed(2).replace(".", ",")}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )
            )}
          </ScrollView>
        </View>

        {/* Detail Modal */}
        <Modal
          visible={selectedPurchase !== null}
          transparent
          animationType="slide"
          onRequestClose={() => setSelectedPurchase(null)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setSelectedPurchase(null)}
          >
            <TouchableOpacity
              style={styles.modalContent}
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
            >
              {/* Drag Indicator */}
              <View style={styles.dragIndicator} />

              {/* Header */}
              <Text style={styles.modalTitle}>Detalhes da compra</Text>

              {selectedPurchase && (
                <ScrollView
                  style={styles.modalScrollView}
                  showsVerticalScrollIndicator={false}
                >
                  {/* Shopping Bag Icon */}
                  <View style={styles.iconContainer}>
                    <Text style={styles.iconText}>🛍️</Text>
                  </View>

                  {/* Purchase Title */}
                  <Text style={styles.purchaseTitleModal}>
                    {selectedPurchase.title}
                  </Text>

                  {/* Debug info - remove after testing */}
                  <Text
                    style={{
                      color: "red",
                      textAlign: "center",
                      marginVertical: 10
                    }}
                  >
                    Debug:{" "}
                    {selectedPurchase ? "Purchase exists" : "No purchase"}
                  </Text>

                  {/* General Information Section */}
                  <View style={styles.infoSection}>
                    <Text style={styles.infoSectionTitle}>
                      Informações gerais
                    </Text>

                    <View style={[styles.infoRow, styles.infoRowHighlighted]}>
                      <Text
                        style={[
                          styles.infoLabel,
                          {backgroundColor: "rgba(255,0,0,0.2)"}
                        ]}
                      >
                        Status
                      </Text>
                      <Text
                        style={[
                          styles.infoValue,
                          {backgroundColor: "rgba(0,255,0,0.2)"},
                          getStatusStyles(selectedPurchase.status).text
                        ]}
                      >
                        {getStatusText(selectedPurchase.status)}
                      </Text>
                    </View>

                    <View style={styles.infoRow}>
                      <Text
                        style={[
                          styles.infoLabel,
                          {backgroundColor: "rgba(255,0,0,0.2)"}
                        ]}
                      >
                        Valor do item
                      </Text>
                      <Text
                        style={[
                          styles.infoValue,
                          {backgroundColor: "rgba(0,255,0,0.2)"}
                        ]}
                      >
                        R${" "}
                        {selectedPurchase.amount.toFixed(2).replace(".", ",")}
                      </Text>
                    </View>

                    <View style={[styles.infoRow, styles.infoRowHighlighted]}>
                      <Text style={styles.infoLabel}>Data/Hora</Text>
                      <Text style={styles.infoValue}>
                        {formatDate(selectedPurchase.date)} -{" "}
                        {formatTime(selectedPurchase.date)}
                      </Text>
                    </View>

                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>Tipo de item</Text>
                      <Text style={styles.infoValue}>
                        {selectedPurchase.itemType}
                      </Text>
                    </View>

                    <View
                      style={[
                        styles.infoRow,
                        styles.infoRowHighlighted,
                        styles.infoRowMultiline
                      ]}
                    >
                      <Text style={styles.infoLabel}>Tipo de pagamento</Text>
                      <Text
                        style={[styles.infoValue, styles.infoValueMultiline]}
                      >
                        {selectedPurchase.paymentMethod}
                      </Text>
                    </View>

                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>Qtde. de parcelas</Text>
                      <Text style={styles.infoValue}>2x sem juros</Text>
                    </View>

                    <View style={[styles.infoRow, styles.infoRowHighlighted]}>
                      <Text style={styles.infoLabel}>Nº pedido</Text>
                      <Text style={styles.infoValue}>
                        {selectedPurchase.orderNumber}
                      </Text>
                    </View>
                  </View>

                  {/* Close Button */}
                  <TouchableOpacity
                    style={styles.closeButtonModal}
                    onPress={() => setSelectedPurchase(null)}
                  >
                    <Text style={styles.closeButtonTextModal}>Fechar</Text>
                  </TouchableOpacity>
                </ScrollView>
              )}
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      </View>
    </ScreenWithHeader>
  );
};

export default HistoryPurchases;
