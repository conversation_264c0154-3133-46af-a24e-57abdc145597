import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingTop: 8
  },
  tabContainer: {
    flexDirection: "row",
    gap: 16,
    paddingLeft: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085"
  },
  tab: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    paddingTop: 1,
    minHeight: 32,
    justifyContent: "center",
    alignItems: "flex-start",
    borderBottomWidth: 2,
    borderBottomColor: "transparent"
  },
  activeTab: {
    borderBottomColor: "#FCFCFD",
    borderBottomWidth: 2
  },
  tabText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.7
  },
  activeTabText: {
    fontWeight: "700",
    opacity: 1,
    color: "#FCFCFD"
  },
  monthLabel: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    marginTop: 12,
    marginLeft: 24,
    marginRight: 24,
    minHeight: 18
  },
  purchaseItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    marginBottom: 4,
    marginLeft: 24,
    marginRight: 24,
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  purchaseIcon: {
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 2,
    marginBottom: 2
  },
  purchaseContent: {
    flex: 1,
    justifyContent: "center",
    minHeight: 36
  },
  purchaseTitle: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    lineHeight: 18,
    flex: 1,
    marginRight: 8
  },
  purchaseDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8,
    flex: 1,
    marginRight: 8
  },
  purchaseRight: {
    gap: 2,
    flexDirection: "column",
    alignItems: "stretch",
    minWidth: 52
  },
  purchaseStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginBottom: 6
  },
  statusText: {
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 18,
    minHeight: 18,
    textOverflow: "ellipsis"
  },
  purchasePrice: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 16,
    minHeight: 16
  },
  cancelledPrice: {
    textDecorationLine: "line-through"
  },
  // Status colors
  pendingStatus: {
    backgroundColor: "transparent"
  },
  pendingText: {
    color: "#FDB022"
  },
  completedStatus: {
    backgroundColor: "transparent"
  },
  completedText: {
    color: "#47CD89"
  },
  cancelledStatus: {
    backgroundColor: "transparent"
  },
  cancelledText: {
    color: "#F97066"
  },
  // Detail Modal Styles - Updated to match Motiff prototype
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(138, 138, 138, 0.6)", // #8A8A8A with opacity as shown in prototype
    justifyContent: "flex-end"
  },
  modalContent: {
    backgroundColor: stylesConstants.colors.mainBackground, // #111828
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderWidth: 1.5,
    borderColor: "#282A2E",
    borderBottomWidth: 0,
    paddingTop: 8,
    paddingHorizontal: 0,
    paddingBottom: 34,
    maxHeight: "90%",
    overflow: "hidden"
  },
  dragIndicator: {
    width: 44,
    height: 4,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 2,
    alignSelf: "center",
    marginBottom: 24
  },
  modalTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 16,
    paddingHorizontal: 110
  },
  modalScrollView: {
    flex: 1,
    paddingHorizontal: 0
  },
  iconContainer: {
    width: 40,
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.20)",
    borderRadius: 40,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: 8
  },
  iconText: {
    fontSize: 20,
    color: "#EAECF0"
  },
  purchaseTitleModal: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    paddingHorizontal: 24,
    marginBottom: 16,
    minHeight: 48
  },
  infoSection: {
    backgroundColor: "#202938",
    borderRadius: 8,
    marginHorizontal: 22,
    padding: 16,
    marginBottom: 24
  },
  infoSectionTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 16
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingHorizontal: 8,
    paddingVertical: 8,
    gap: 16
  },
  infoRowHighlighted: {
    backgroundColor: "#344054",
    borderRadius: 4,
    marginHorizontal: -8
  },
  infoRowMultiline: {
    alignItems: "flex-start"
  },
  infoLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    width: 133.5,
    minHeight: 18
  },
  infoValue: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    textAlign: "right",
    width: 133.5,
    minHeight: 18
  },
  infoValueMultiline: {
    minHeight: 36,
    textAlign: "right"
  },
  closeButtonModal: {
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 18,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginTop: 24,
    marginBottom: 24
  },
  closeButtonTextModal: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  },
  // Legacy styles - keeping for backward compatibility
  detailSection: {
    marginBottom: 24
  },
  detailSectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    marginBottom: 16
  },
  detailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.08)"
  },
  detailItemLast: {
    borderBottomWidth: 0
  },
  detailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8
  },
  detailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
    textAlign: "right",
    flex: 1,
    marginLeft: 16
  },
  closeButton: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24
  },
  closeButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
